'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First, add new columns to ExperienceEnrollment table
    await queryInterface.addColumn('ExperienceEnrollment', 'currentWeek', {
      type: Sequelize.INTEGER,
      defaultValue: 1,
      allowNull: false,
      validate: {
        min: 1,
      },
      comment: 'Current week number the user is on',
    });

    await queryInterface.addColumn('ExperienceEnrollment', 'completedWeeks', {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      allowNull: false,
      validate: {
        min: 0,
      },
      comment: 'Total number of weeks completed',
    });

    // Create ExperienceWeekProgress table
    await queryInterface.createTable('ExperienceWeekProgress', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      enrollmentId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceEnrollment',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      experienceWeekId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeek',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      weekNumber: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
        },
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'COMPLETED'),
        defaultValue: 'PENDING',
        allowNull: false,
      },
      completedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex('ExperienceWeekProgress', ['enrollmentId'], {
      name: 'experience_week_progress_enrollment_id_idx',
    });

    await queryInterface.addIndex(
      'ExperienceWeekProgress',
      ['experienceWeekId'],
      {
        name: 'experience_week_progress_week_id_idx',
      }
    );

    await queryInterface.addIndex('ExperienceWeekProgress', ['status'], {
      name: 'experience_week_progress_status_idx',
    });

    // Add unique constraints
    await queryInterface.addConstraint('ExperienceWeekProgress', {
      fields: ['enrollmentId', 'experienceWeekId'],
      type: 'unique',
      name: 'experience_week_progress_unique_idx',
    });

    await queryInterface.addConstraint('ExperienceWeekProgress', {
      fields: ['enrollmentId', 'weekNumber'],
      type: 'unique',
      name: 'experience_week_progress_enrollment_week_unique_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    // Drop ExperienceWeekProgress table
    await queryInterface.dropTable('ExperienceWeekProgress');

    // Remove columns from ExperienceEnrollment table
    await queryInterface.removeColumn('ExperienceEnrollment', 'currentWeek');
    await queryInterface.removeColumn('ExperienceEnrollment', 'completedWeeks');
  },
};
