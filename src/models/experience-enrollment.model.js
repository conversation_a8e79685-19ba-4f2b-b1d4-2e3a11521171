/**
 * ExperienceEnrollment Model
 * Represents a user's enrollment in an experience
 */
const { Model, DataTypes } = require('sequelize');
const { ExperienceEnrollmentStatus } = require('@utils/enums.utils');

class ExperienceEnrollment extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Experience',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        startDate: {
          type: DataTypes.DATEONLY,
          allowNull: false,
          validate: {
            isDate: true,
          },
        },
        status: {
          type: DataTypes.ENUM(...ExperienceEnrollmentStatus.values),
          defaultValue: ExperienceEnrollmentStatus.REGISTERED,
          allowNull: false,
          validate: {
            isIn: [ExperienceEnrollmentStatus.values],
          },
        },
        currentWeek: {
          type: DataTypes.INTEGER,
          defaultValue: 1,
          allowNull: false,
          validate: {
            min: 1,
          },
          comment: 'Current week number the user is on',
        },
        completedWeeks: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
          allowNull: false,
          validate: {
            min: 0,
          },
          comment: 'Total number of weeks completed',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceEnrollment',
        tableName: 'ExperienceEnrollment',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceId'],
            name: 'experience_enrollment_experience_id_idx',
          },
          {
            fields: ['userId'],
            name: 'experience_enrollment_user_id_idx',
          },
          {
            fields: ['status'],
            name: 'experience_enrollment_status_idx',
          },
          {
            unique: true,
            fields: ['experienceId', 'userId'],
            name: 'experience_enrollment_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    this.belongsTo(models.Experience, {
      foreignKey: 'experienceId',
      as: 'experience',
      onDelete: 'CASCADE',
    });

    this.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    // Has many week progress records
    this.hasMany(models.ExperienceWeekProgress, {
      foreignKey: 'enrollmentId',
      as: 'weekProgress',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = ExperienceEnrollment;
