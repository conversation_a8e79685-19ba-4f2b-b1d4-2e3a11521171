/**
 * ExperienceWeekProgress Model
 * Tracks individual week completion progress for user enrollments
 */
const { Model, DataTypes } = require('sequelize');
const { ExperienceWeekProgressStatus } = require('@utils/enums.utils');

class ExperienceWeekProgress extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        enrollmentId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceEnrollment',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        experienceWeekId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeek',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        weekNumber: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
          },
        },
        status: {
          type: DataTypes.ENUM(...ExperienceWeekProgressStatus.values),
          defaultValue: ExperienceWeekProgressStatus.PENDING,
          allowNull: false,
          validate: {
            isIn: [ExperienceWeekProgressStatus.values],
          },
        },
        completedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekProgress',
        tableName: 'ExperienceWeekProgress',
        timestamps: true,
        indexes: [
          {
            fields: ['enrollmentId'],
            name: 'experience_week_progress_enrollment_id_idx',
          },
          {
            fields: ['experienceWeekId'],
            name: 'experience_week_progress_week_id_idx',
          },
          {
            fields: ['status'],
            name: 'experience_week_progress_status_idx',
          },
          {
            unique: true,
            fields: ['enrollmentId', 'experienceWeekId'],
            name: 'experience_week_progress_unique_idx',
          },
          {
            unique: true,
            fields: ['enrollmentId', 'weekNumber'],
            name: 'experience_week_progress_enrollment_week_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to ExperienceEnrollment
    this.belongsTo(models.ExperienceEnrollment, {
      foreignKey: 'enrollmentId',
      as: 'enrollment',
      onDelete: 'CASCADE',
    });

    // Belongs to ExperienceWeek
    this.belongsTo(models.ExperienceWeek, {
      foreignKey: 'experienceWeekId',
      as: 'experienceWeek',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };

    // Add computed fields
    values.isCompleted = this.status === ExperienceWeekProgressStatus.COMPLETED;
    values.isPending = this.status === ExperienceWeekProgressStatus.PENDING;

    return values;
  }

  // Mark week as completed
  async markCompleted() {
    return this.update({
      status: ExperienceWeekProgressStatus.COMPLETED,
      completedAt: new Date(),
    });
  }

  // Reset week to pending
  async resetToPending() {
    return this.update({
      status: ExperienceWeekProgressStatus.PENDING,
      completedAt: null,
    });
  }
}

module.exports = ExperienceWeekProgress;
