/**
 * ExperienceWeekProgress Repository
 * Handles database operations for ExperienceWeekProgress model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const {
  HttpStatus,
  ExperienceWeekProgressStatus,
} = require('@utils/enums.utils');

/**
 * Experience week progress repository methods
 */
class ExperienceWeekProgressRepository {
  constructor() {
    this.models = {
      ExperienceWeekProgress: databaseService.getExperienceWeekProgressModel(),
      ExperienceEnrollment: databaseService.getExperienceEnrollmentModel(),
      ExperienceWeek: databaseService.getExperienceWeekModel(),
    };
  }

  /**
   * Find progress by ID
   * @param {string} id - Progress ID
   * @returns {Promise<Object>} Progress object
   */
  async findById(id) {
    return this.models.ExperienceWeekProgress.findByPk(id, {
      include: [
        {
          model: this.models.ExperienceWeek,
          as: 'experienceWeek',
          attributes: ['id', 'weekNumber', 'title', 'weeklyWhy'],
        },
      ],
    });
  }

  /**
   * Find progress by enrollment and week
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number
   * @returns {Promise<Object>} Progress object
   */
  async findByEnrollmentAndWeek(enrollmentId, weekNumber) {
    return this.models.ExperienceWeekProgress.findOne({
      where: { enrollmentId, weekNumber },
      include: [
        {
          model: this.models.ExperienceWeek,
          as: 'experienceWeek',
          attributes: ['id', 'weekNumber', 'title', 'weeklyWhy'],
        },
      ],
    });
  }

  /**
   * Find all progress for an enrollment
   * @param {string} enrollmentId - Enrollment ID
   * @returns {Promise<Array>} Array of progress objects
   */
  async findByEnrollment(enrollmentId) {
    return this.models.ExperienceWeekProgress.findAll({
      where: { enrollmentId },
      include: [
        {
          model: this.models.ExperienceWeek,
          as: 'experienceWeek',
          attributes: ['id', 'weekNumber', 'title', 'weeklyWhy'],
        },
      ],
      order: [['weekNumber', 'ASC']],
    });
  }

  /**
   * Create progress record
   * @param {Object} data - Progress data
   * @returns {Promise<Object>} Created progress
   */
  async create(data) {
    return this.models.ExperienceWeekProgress.create(data);
  }

  /**
   * Create multiple progress records
   * @param {Array} progressData - Array of progress data
   * @returns {Promise<Array>} Created progress records
   */
  async bulkCreate(progressData) {
    return this.models.ExperienceWeekProgress.bulkCreate(progressData);
  }

  /**
   * Update progress
   * @param {string} id - Progress ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated progress
   */
  async update(id, data) {
    const progress = await this.findById(id);
    if (!progress) {
      throw new ApiException(HttpStatus.NOT_FOUND, 'Week progress not found');
    }

    return progress.update(data);
  }

  /**
   * Mark week as completed
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number
   * @returns {Promise<Object>} Updated progress
   */
  async markWeekCompleted(enrollmentId, weekNumber) {
    const progress = await this.findByEnrollmentAndWeek(
      enrollmentId,
      weekNumber
    );
    if (!progress) {
      throw new ApiException(HttpStatus.NOT_FOUND, 'Week progress not found');
    }

    return progress.update({
      status: ExperienceWeekProgressStatus.COMPLETED,
      completedAt: new Date(),
    });
  }

  /**
   * Get completion statistics for an enrollment
   * @param {string} enrollmentId - Enrollment ID
   * @returns {Promise<Object>} Completion statistics
   */
  async getCompletionStats(enrollmentId) {
    const progressRecords = await this.findByEnrollment(enrollmentId);

    const totalWeeks = progressRecords.length;
    const completedWeeks = progressRecords.filter(
      (p) => p.status === ExperienceWeekProgressStatus.COMPLETED
    ).length;

    const currentWeek =
      progressRecords.find(
        (p) => p.status === ExperienceWeekProgressStatus.PENDING
      )?.weekNumber || completedWeeks + 1;

    return {
      totalWeeks,
      completedWeeks,
      currentWeek: currentWeek > totalWeeks ? totalWeeks : currentWeek,
      progressPercentage:
        totalWeeks > 0 ? Math.round((completedWeeks / totalWeeks) * 100) : 0,
      isCompleted: completedWeeks === totalWeeks && totalWeeks > 0,
    };
  }

  /**
   * Check if user can access a specific week
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number to check
   * @returns {Promise<boolean>} Whether user can access the week
   */
  async canAccessWeek(enrollmentId, weekNumber) {
    if (weekNumber === 1) return true; // First week is always accessible

    // Check if previous week is completed
    const previousWeekProgress = await this.findByEnrollmentAndWeek(
      enrollmentId,
      weekNumber - 1
    );

    return (
      previousWeekProgress &&
      previousWeekProgress.status === ExperienceWeekProgressStatus.COMPLETED
    );
  }

  /**
   * Get next available week for user
   * @param {string} enrollmentId - Enrollment ID
   * @returns {Promise<number>} Next available week number
   */
  async getNextAvailableWeek(enrollmentId) {
    const stats = await this.getCompletionStats(enrollmentId);
    return Math.min(stats.completedWeeks + 1, stats.totalWeeks);
  }
}

module.exports = new ExperienceWeekProgressRepository();
