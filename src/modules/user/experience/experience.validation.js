/**
 * Experience Validation
 *
 * Validation schemas for experience operations
 */
const { body, param, query } = require('express-validator');

/**
 * Experience validation schemas
 */
const experienceValidation = {
  /**
   * Create experience validation schema
   */
  create: [
    body('title').notEmpty().withMessage('Experience title is required'),
    body('experienceLength')
      .isInt({ min: 1, max: 52 })
      .withMessage('Experience length must be between 1 and 52 weeks'),
    body('shortDescription').optional().isString(),
    body('longDescription').optional().isString(),
    body('personalNote').optional().isString(),
    body('pdCategoryIds')
      .optional()
      .isArray()
      .withMessage('PD categories must be an array'),
    body('pdCategoryIds.*')
      .optional()
      .isUUID()
      .withMessage('Each PD category ID must be a valid UUID'),
    body('wtdCategoryIds')
      .optional()
      .isArray()
      .withMessage('WTD categories must be an array'),
    body('wtdCategoryIds.*')
      .optional()
      .isUUID()
      .withMessage('Each WTD category ID must be a valid UUID'),
    body('media').optional().isArray().withMessage('Media must be an array'),
    body('media.*.type')
      .optional()
      .isIn(['IMAGE', 'VIDEO', 'DOCUMENT', 'LINK', 'AUDIO'])
      .withMessage('Media type must be IMAGE, VIDEO, DOCUMENT, LINK, or AUDIO'),
    body('media.*.url')
      .optional()
      .isString()
      .withMessage('Media URL must be a string'),
    body('media.*.title')
      .optional()
      .isString()
      .withMessage('Media title must be a string'),
    body('weeks').optional().isArray().withMessage('Weeks must be an array'),
    body('weeks.*.weekNumber')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Week number must be a positive integer'),
    body('weeks.*.title')
      .optional()
      .isString()
      .withMessage('Week title must be a string'),
    body('weeks.*.weeklyWhy')
      .optional()
      .isString()
      .withMessage('Weekly why must be a string'),
    body('weeks.*.insights')
      .optional()
      .isArray({ max: 5 })
      .withMessage('Insights must be an array with maximum 5 items'),
    body('weeks.*.insights.*.text')
      .optional()
      .notEmpty()
      .withMessage('Insight text is required'),
    body('weeks.*.insights.*.sourceUrl')
      .optional()
      .isString()
      .withMessage('Source URL must be a string'),
  ],

  /**
   * Get all experiences validation schema
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt(),
  ],

  /**
   * Get experience by ID validation schema
   */
  getById: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
  ],

  /**
   * Enroll user validation schema
   */
  enrollUser: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
    body('startDate')
      .isISO8601()
      .withMessage('Start date must be a valid ISO date')
      .custom((value) => {
        const date = new Date(value);
        return date.getDay() === 1; // Monday is 1
      })
      .withMessage('Start date must be a Monday'),
  ],

  /**
   * Update enrollment status validation schema
   */
  updateEnrollmentStatus: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
    body('status')
      .isIn(['REGISTERED', 'COMPLETED'])
      .withMessage('Status must be either REGISTERED or COMPLETED'),
  ],

  /**
   * Complete week validation schema
   */
  completeWeek: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
    param('weekNumber')
      .isInt({ min: 1, max: 52 })
      .withMessage('Week number must be between 1 and 52'),
  ],

  /**
   * Submit experience review validation schema
   */
  submitReview: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
    body('courseRating')
      .isInt({ min: 1, max: 5 })
      .withMessage('Course rating must be an integer between 1 and 5'),
    body('providerRating')
      .isInt({ min: 1, max: 5 })
      .withMessage('Provider rating must be an integer between 1 and 5'),
    body('reviewText')
      .optional()
      .isString()
      .withMessage('Review text must be a string'),
  ],

  /**
   * Create discussion validation schema
   */
  createDiscussion: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
    body('content')
      .isString()
      .notEmpty()
      .withMessage('Discussion content is required')
      .isLength({ min: 1, max: 1000 })
      .withMessage('Discussion content must be between 1 and 1000 characters'),
  ],

  /**
   * Get discussions validation schema
   */
  getDiscussions: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
  ],

  /**
   * Toggle discussion like validation schema
   */
  toggleDiscussionLike: [
    param('discussionId').isUUID().withMessage('Invalid discussion ID format'),
  ],
};

module.exports = experienceValidation;
