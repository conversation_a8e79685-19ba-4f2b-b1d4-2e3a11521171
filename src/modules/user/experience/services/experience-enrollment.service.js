/**
 * Experience Enrollment Service
 *
 * Handles business logic for experience enrollments
 */
const experienceEnrollmentRepository = require('@models/repositories/experience-enrollment.repository');
const experienceRepository = require('@models/repositories/experience.repository');
const { ApiException } = require('@utils/exception.utils');
const { EXPERIENCE_ENROLLMENT } = require('@utils/messages.utils');
const {
  ExperienceEnrollmentStatus,
  HttpStatus,
} = require('@utils/enums.utils');

/**
 * Experience Enrollment Service
 */
const experienceEnrollmentService = {
  /**
   * Enroll a user in an experience
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date for the experience
   * @returns {Promise<Object>} Created enrollment
   */
  enrollUser: async (experienceId, userId, startDate) => {
    try {
      // Check if experience exists and get creator ID
      const creatorId = await experienceRepository.findCreatorId(experienceId);
      if (!creatorId) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.EXPERIENCE_NOT_FOUND
        );
      }

      // Check if user is the creator of the experience
      if (creatorId === userId) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE_ENROLLMENT.CANNOT_ENROLL_OWN_EXPERIENCE
        );
      }

      // Check if user is already enrolled
      const existingEnrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );
      if (existingEnrollment) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE_ENROLLMENT.ALREADY_ENROLLED
        );
      }

      // Create enrollment
      const enrollment = await experienceEnrollmentRepository.create({
        experienceId,
        userId,
        startDate,
        status: ExperienceEnrollmentStatus.REGISTERED,
      });

      return enrollment;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update enrollment status
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated enrollment
   */
  updateEnrollmentStatus: async (experienceId, userId, status) => {
    try {
      // Check if enrollment exists and belongs to user
      const enrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );

      if (!enrollment) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.NOT_FOUND
        );
      }

      // Update status
      const updatedEnrollment = await experienceEnrollmentRepository.update(
        enrollment.id,
        { status }
      );

      return updatedEnrollment;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceEnrollmentService;
