/**
 * Experience Enrollment Service
 *
 * Handles business logic for experience enrollments
 */
const experienceEnrollmentRepository = require('@models/repositories/experience-enrollment.repository');
const experienceRepository = require('@models/repositories/experience.repository');
const experienceWeekProgressRepository = require('@models/repositories/experience-week-progress.repository');
const { ApiException } = require('@utils/exception.utils');
const { EXPERIENCE_ENROLLMENT } = require('@utils/messages.utils');
const {
  ExperienceEnrollmentStatus,
  ExperienceWeekProgressStatus,
  HttpStatus,
} = require('@utils/enums.utils');

/**
 * Experience Enrollment Service
 */
const experienceEnrollmentService = {
  /**
   * Enroll a user in an experience
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date for the experience
   * @returns {Promise<Object>} Created enrollment
   */
  enrollUser: async (experienceId, userId, startDate) => {
    try {
      // Check if experience exists and get creator ID
      const creatorId = await experienceRepository.findCreatorId(experienceId);
      if (!creatorId) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.EXPERIENCE_NOT_FOUND
        );
      }

      // Check if user is the creator of the experience
      if (creatorId === userId) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE_ENROLLMENT.CANNOT_ENROLL_OWN_EXPERIENCE
        );
      }

      // Check if user is already enrolled
      const existingEnrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );
      if (existingEnrollment) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE_ENROLLMENT.ALREADY_ENROLLED
        );
      }

      // Create enrollment
      const enrollment = await experienceEnrollmentRepository.create({
        experienceId,
        userId,
        startDate,
        status: ExperienceEnrollmentStatus.REGISTERED,
        currentWeek: 1,
        completedWeeks: 0,
      });

      // Initialize week progress records
      await this.initializeWeekProgress(enrollment.id, experienceId);

      return enrollment;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update enrollment status
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated enrollment
   */
  updateEnrollmentStatus: async (experienceId, userId, status) => {
    try {
      // Check if enrollment exists and belongs to user
      const enrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );

      if (!enrollment) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.NOT_FOUND
        );
      }

      // Update status
      const updatedEnrollment = await experienceEnrollmentRepository.update(
        enrollment.id,
        { status }
      );

      return updatedEnrollment;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Initialize week progress records for an enrollment
   * @param {string} enrollmentId - Enrollment ID
   * @param {string} experienceId - Experience ID
   * @returns {Promise<void>}
   */
  initializeWeekProgress: async (enrollmentId, experienceId) => {
    try {
      // Get all weeks for the experience
      const experience = await experienceRepository.getExperienceDetails(
        experienceId,
        true
      );

      if (!experience || !experience.weeks || experience.weeks.length === 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          'Experience has no weeks defined'
        );
      }

      // Create progress records for all weeks
      const progressData = experience.weeks.map((week) => ({
        enrollmentId,
        experienceWeekId: week.id,
        weekNumber: week.weekNumber,
        status: ExperienceWeekProgressStatus.PENDING,
      }));

      await experienceWeekProgressRepository.bulkCreate(progressData);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Complete a specific week for a user
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {number} weekNumber - Week number to complete
   * @returns {Promise<Object>} Updated enrollment with progress
   */
  completeWeek: async (experienceId, userId, weekNumber) => {
    try {
      // Get enrollment
      const enrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );

      if (!enrollment) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.NOT_FOUND
        );
      }

      // Check if user can complete this week (sequential completion)
      if (weekNumber > 1) {
        const canAccess = await experienceWeekProgressRepository.canAccessWeek(
          enrollment.id,
          weekNumber
        );

        if (!canAccess) {
          throw new ApiException(
            HttpStatus.BAD_REQUEST,
            'You must complete previous weeks before accessing this week'
          );
        }
      }

      // Mark week as completed
      await experienceWeekProgressRepository.markWeekCompleted(
        enrollment.id,
        weekNumber
      );

      // Update enrollment progress
      const stats = await experienceWeekProgressRepository.getCompletionStats(
        enrollment.id
      );

      const updateData = {
        completedWeeks: stats.completedWeeks,
        currentWeek: stats.currentWeek,
      };

      // If all weeks completed, mark enrollment as completed
      if (stats.isCompleted) {
        updateData.status = ExperienceEnrollmentStatus.COMPLETED;
      }

      const updatedEnrollment = await experienceEnrollmentRepository.update(
        enrollment.id,
        updateData
      );

      return {
        enrollment: updatedEnrollment,
        progress: stats,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get detailed progress for an enrollment
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Detailed progress information
   */
  getEnrollmentProgress: async (experienceId, userId) => {
    try {
      const enrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );

      if (!enrollment) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.NOT_FOUND
        );
      }

      const weekProgress =
        await experienceWeekProgressRepository.findByEnrollment(enrollment.id);
      const stats = await experienceWeekProgressRepository.getCompletionStats(
        enrollment.id
      );

      return {
        enrollment,
        weekProgress,
        stats,
      };
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceEnrollmentService;
